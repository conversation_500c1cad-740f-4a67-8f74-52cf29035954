"use client"

import { useState, use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON> } from "react"
import { format } from "date-fns"
import { Search, Filter, CalendarIcon, X, Loader2, ChevronLeft, ChevronRight } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { ParcelDetailsDialog } from "@/components/parcel-details-dialog"
import { ParcelStatusDialog } from "@/components/parcel-status-dialog"
import { ParcelDeliveryDialog } from "@/components/parcel-delivery-dialog"
import { useToast } from "@/hooks/use-toast"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Parcel, Branch, ParcelType } from "@/lib/db-helpers"
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"

// Dynamic branch data will be loaded from API

// Mock data
const mockParcels = [
  {
    lr: "LR123456",
    bookingDate: "2024-03-20T10:30:00",
    status: "booked",
    senderName: "Rajesh Kumar",
    senderAddress: "45, Gandhi Street, T Nagar, Chennai",
    senderPhone: "+91 98765 43210",
    recipientName: "Priya Sundaram",
    recipientAddress: "123, Anna Salai, Coimbatore",
    recipientPhone: "+91 87654 32109",
    weight: "2.5",
    dimensions: "30x20x15",
    price: "450.00",
    paymentType: "paid",
    instructions: "Handle with care",
    items: [
      {
        type: "Electronics",
        description: "Mobile Phone",
        quantity: 1,
        weight: "0.5",
        amount: 250
      },
      {
        type: "Accessories",
        description: "Phone Charger",
        quantity: 1,
        weight: "2.0",
        amount: 200
      }
    ],
    statusHistory: [
      {
        status: "Booked",
        location: "Chennai - T Nagar Branch",
        date: "2024-03-20T10:30:00",
        remarks: "Parcel received at booking office"
      },
      {
        status: "In Transit",
        location: "Chennai Hub",
        date: "2024-03-20T15:45:00",
        vehicleNo: "TN01AB1234",
        luggageNo: "LUG789",
        remarks: "Dispatched to destination hub"
      }
    ]
  },
  {
    lr: "LR789012",
    bookingDate: "2024-03-19T14:15:00",
    status: "to_be_delivered",
    senderName: "Amit Shah",
    senderAddress: "78, MG Road, Bangalore",
    senderPhone: "+91 97654 32109",
    recipientName: "Karthik Raman",
    recipientAddress: "456, 100 Feet Road, Chennai",
    recipientPhone: "+91 86543 21098",
    weight: "1.8",
    dimensions: "25x15x10",
    price: "350.00",
    paymentType: "to_pay",
    instructions: "Call before delivery",
    items: [
      {
        type: "Documents",
        description: "Legal Documents",
        quantity: 1,
        weight: "1.8",
        amount: 350
      }
    ],
    statusHistory: [
      {
        status: "Booked",
        location: "Bangalore Main Branch",
        date: "2024-03-19T14:15:00",
        remarks: "Parcel booked"
      },
      {
        status: "In Transit",
        location: "Bangalore Hub",
        date: "2024-03-19T18:30:00",
        vehicleNo: "KA01CD5678",
        luggageNo: "LUG456",
        remarks: "En route to Chennai"
      }
    ]
  },
  {
    lr: "LR345678",
    bookingDate: "2024-03-18T09:45:00",
    status: "delivered",
    senderName: "Meera Patel",
    senderAddress: "23, Lake View Road, Mumbai",
    senderPhone: "+91 96543 21087",
    recipientName: "Suresh Kumar",
    recipientAddress: "789, Beach Road, Chennai",
    recipientPhone: "+91 85432 10987",
    weight: "3.2",
    dimensions: "40x30x20",
    price: "600.00",
    paymentType: "paid",
    instructions: "Fragile items",
    items: [
      {
        type: "Home Decor",
        description: "Glass Vase",
        quantity: 2,
        weight: "1.5",
        amount: 300
      },
      {
        type: "Gift Items",
        description: "Photo Frames",
        quantity: 3,
        weight: "1.7",
        amount: 300
      }
    ],
    statusHistory: [
      {
        status: "Booked",
        location: "Mumbai Central Branch",
        date: "2024-03-18T09:45:00",
        remarks: "Parcel received"
      },
      {
        status: "Delivered",
        location: "Chennai Beach Road Branch",
        date: "2024-03-20T11:30:00",
        remarks: "Successfully delivered to recipient"
      }
    ]
  }
]

const statusColors = {
  "Booked": "bg-blue-500",
  "Loaded": "bg-purple-500",
  "Received": "bg-green-500",
  "Delivered": "bg-gray-500"
}

// Dynamic status labels based on database enum
const statusLabels = {
  "Booked": "Booked",
  "Loaded": "Loaded",
  "Received": "Received",
  "Delivered": "Delivered"
}

const searchTypes = [
  { value: "lr_number", label: "LR Number" },
  { value: "phone", label: "Phone Number" },
  { value: "sender", label: "Sender Name" },
  { value: "recipient", label: "Recipient Name" }
]

const paymentTypeLabels = {
  "Paid": "Paid",
  "To Pay": "To Pay"
}

const filterParcels = (parcels: Parcel[]) => {
  return parcels.filter(parcel => {
    // Basic search filter
    const matchesSearch = searchQuery
      ? parcel[searchType].toLowerCase().includes(searchQuery.toLowerCase())
      : true;

    // Status filter
    const matchesStatus = selectedStatus === "all"
      ? true
      : parcel.status === selectedStatus;

    // Branch filter
    const matchesBranch = selectedBranch
      ? (branchType === "sender"
          ? parcel.senderBranch === selectedBranch
          : parcel.receiverBranch === selectedBranch)
      : true;

    // Date filter
    const matchesDate = selectedDate
      ? format(new Date(parcel.bookingDate), "yyyy-MM-dd") === format(selectedDate, "yyyy-MM-dd")
      : true;

    return matchesSearch && matchesStatus && matchesBranch && matchesDate;
  });
};

export function ParcelList() {
  const [searchType, setSearchType] = useState("lr_number")
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedParcel, setSelectedParcel] = useState<Parcel | null>(null)
  const [isDetailsOpen, setIsDetailsOpen] = useState(false)
  const [isStatusUpdateOpen, setIsStatusUpdateOpen] = useState(false)
  const [isDeliveryDialogOpen, setIsDeliveryDialogOpen] = useState(false)
  const [selectedDate, setSelectedDate] = useState<Date>()
  const [branchType, setBranchType] = useState<"sender" | "receiver">("sender")
  const [selectedCity, setSelectedCity] = useState<string>("")
  const [selectedBranch, setSelectedBranch] = useState<string>("")
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [parcels, setParcels] = useState<Parcel[]>([])
  const [loading, setLoading] = useState(true)
  const [branchList, setBranchList] = useState<Branch[]>([])
  const [parcelTypes, setParcelTypes] = useState<ParcelType[]>([])
  const [pagination, setPagination] = useState({
    currentPage: 1,
    pageSize: 20,
    totalItems: 0,
    totalPages: 1
  })
  const [userBranchId, setUserBranchId] = useState<string | null>(null)
  const [showDeliverableOnly, setShowDeliverableOnly] = useState(true) // Default to deliverable parcels
  const { toast } = useToast()

  // Get user branch ID from localStorage
  useEffect(() => {
    const branchId = localStorage.getItem('userBranchId')
    setUserBranchId(branchId)
  }, [])

  // Fetch parcels with pagination
  const fetchParcels = async (page: number = 1) => {
    setLoading(true);
    try {
      // Choose API endpoint based on view mode
      const apiEndpoint = showDeliverableOnly ? '/api/parcels/delivery-eligible' : '/api/parcels';
      const url = new URL(apiEndpoint, window.location.origin);
      url.searchParams.append('page', page.toString());
      url.searchParams.append('pageSize', pagination.pageSize.toString());

      // Add filters if any (only for all parcels view)
      if (!showDeliverableOnly) {
        if (selectedStatus !== "all") {
          url.searchParams.append('current_status', selectedStatus);
        }

        if (selectedBranch) {
          if (branchType === "sender") {
            url.searchParams.append('sender_branch_id', selectedBranch);
          } else {
            url.searchParams.append('delivery_branch_id', selectedBranch);
          }
        }
      }

      // Fetch parcels with pagination
      const parcelsResponse = await fetch(url.toString());
      if (!parcelsResponse.ok) {
        throw new Error('Failed to fetch parcels');
      }

      const data = await parcelsResponse.json();
      setParcels(data.parcels || []);
      setPagination({
        currentPage: data.pagination.page,
        pageSize: data.pagination.pageSize,
        totalItems: data.pagination.total,
        totalPages: data.pagination.totalPages
      });
    } catch (error: any) {
      console.error('Error fetching parcels:', error);
      toast({
        title: 'Error',
        description: 'Failed to load parcels',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch reference data (branches and parcel types)
  const fetchReferenceData = async () => {
    try {
      // Fetch branches
      const branchesResponse = await fetch('/api/branches');
      if (!branchesResponse.ok) {
        throw new Error('Failed to fetch branches');
      }
      const branchesData = await branchesResponse.json();
      setBranchList(branchesData);

      // Fetch parcel types
      const parcelTypesResponse = await fetch('/api/parceltypes');
      if (!parcelTypesResponse.ok) {
        throw new Error('Failed to fetch parcel types');
      }
      const parcelTypesData = await parcelTypesResponse.json();
      setParcelTypes(parcelTypesData);
    } catch (error: any) {
      console.error('Error fetching reference data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load reference data',
        variant: 'destructive',
      });
    }
  };

  // Initial data loading
  useEffect(() => {
    const loadInitialData = async () => {
      await fetchReferenceData();
      await fetchParcels(1);
    };

    loadInitialData();
  }, [toast]);

  // Fetch parcels when filters change
  useEffect(() => {
    if (!loading) {
      fetchParcels(1); // Reset to first page when filters change
    }
  }, [selectedStatus, selectedBranch, branchType, showDeliverableOnly]);

  const handleParcelClick = (parcel: any) => {
    // Make sure we're passing the parcel_id, not the LR number
    if (parcel && parcel.parcel_id) {
      fetchParcelDetails(parcel.parcel_id);
    } else {
      console.error("No parcel_id found in parcel object:", parcel);
      toast({
        title: "Error",
        description: "Could not load parcel details - missing ID",
        variant: "destructive"
      });
    }
  };

  const fetchParcelDetails = async (id: number) => {
    try {
      const response = await fetch(`/api/parcels/${id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch parcel details');
      }
      const data = await response.json();

      // Process status history data
      if (data.status_history) {
        // Convert timestamp to date for compatibility with the UI
        data.status_history = data.status_history.map((entry: any) => ({
          ...entry,
          date: entry.timestamp || entry.date || new Date().toISOString()
        }));

        console.log("Status history data:", data.status_history); // Debug log
      } else {
        console.log("No status history data found in API response"); // Debug log

        // If no status history, create a default entry for "Booked" status
        data.status_history = [{
          status: "Booked",
          date: data.booking_datetime,
          location: data.sender_branch?.name || 'Unknown',
          remarks: 'Parcel booked'
        }];

        console.log("Created default status history:", data.status_history); // Debug log
      }

      setSelectedParcel(data);
      setIsDetailsOpen(true);
    } catch (error) {
      console.error('Error fetching parcel details:', error);
      toast({
        title: "Error",
        description: "Failed to fetch parcel details",
        variant: "destructive"
      });
    }
  };

  // Handle delivery button click
  const handleDeliveryClick = (parcel: Parcel) => {
    setSelectedParcel(parcel);
    setIsDeliveryDialogOpen(true);
  };

  // Handle delivery completion
  const handleDeliveryComplete = () => {
    // Refresh the parcels list
    fetchParcels(pagination.currentPage);
    setSelectedParcel(null);
    setIsDeliveryDialogOpen(false);
  };

  const handleStatusUpdate = async (statusData: any) => {
    const statusLabel = statusLabels[statusData.newStatus as keyof typeof statusLabels]
    let toastMessage = `Parcel has been marked as ${statusLabel}`

    // Add recipient details to toast message if provided
    if (statusData.recipientDetails && statusData.newStatus === "Delivered") {
      const { name, phone } = statusData.recipientDetails
      if (name && phone) {
        toastMessage += ` - Recipient: ${name} (${phone})`
      }
    }

    // Add payment details to toast message if provided
    if (statusData.paymentDetails) {
      const { amount, method } = statusData.paymentDetails
      const paymentMethod = method === 'cash' ? 'Cash' : 'Online'
      toastMessage += ` - Payment of ₹${amount} collected via ${paymentMethod}`
    }

    if (statusData.notes) {
      toastMessage += ` - ${statusData.notes}`
    }

    if (selectedParcel) {
      try {
        // Prepare update data
        const updateData: any = {
          current_status: statusData.newStatus,
        }

        // Add collector details if provided
        if (statusData.collectorDetails && statusData.newStatus === "Delivered") {
          // Store collector details in a JSON field
          updateData.collector_details = JSON.stringify({
            name: statusData.collectorDetails.name,
            phone: statusData.collectorDetails.phone,
            id_type: statusData.collectorDetails.idProof || null,
            id_number: statusData.collectorDetails.idNumber || null
          })

          // Set actual delivery date
          updateData.actual_delivery_date = new Date().toISOString()
        }

        // Update payment mode if payment was collected
        if (statusData.paymentDetails) {
          updateData.payment_mode = 'Paid'
        }

        // Update the parcel status in the database
        const response = await fetch(`/api/parcels/${selectedParcel.parcel_id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...updateData,
            // Add status history entry
            status_history: {
              status: statusData.newStatus, // This is a parcel_status enum value
              timestamp: new Date().toISOString(),
              location: selectedParcel.current_status === "In Transit"
                ? `En route from ${selectedParcel.sender_branch?.name || ''} to ${selectedParcel.delivery_branch?.name || ''}`
                : selectedParcel.current_status === "To Be Delivered" || selectedParcel.current_status === "Delivered"
                  ? selectedParcel.delivery_branch?.name || ''
                  : selectedParcel.sender_branch?.name || '',
              remarks: statusData.notes || '',
              updated_by: "current_user" // This should be replaced with actual user ID
            }
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to update parcel status');
        }

        toast({
          title: "Status Updated",
          description: toastMessage
        });

        // Update the parcels array with the new status and details
        setParcels(currentParcels =>
          currentParcels.map(parcel =>
            parcel.parcel_id === selectedParcel.parcel_id
              ? {
                  ...parcel,
                  current_status: statusData.newStatus,
                  payment_mode: statusData.paymentDetails ? "Paid" : parcel.payment_mode,
                  // Add collector_details if provided
                  ...(statusData.collectorDetails ? {
                    collector_details: JSON.stringify({
                      name: statusData.collectorDetails.name,
                      phone: statusData.collectorDetails.phone,
                      id_type: statusData.collectorDetails.idProof || null,
                      id_number: statusData.collectorDetails.idNumber || null
                    })
                  } : {})
                }
              : parcel
          )
        );
      } catch (error) {
        console.error('Error updating parcel status:', error);
        toast({
          title: "Error",
          description: "Failed to update parcel status",
          variant: "destructive"
        });
      }
    }

    setIsStatusUpdateOpen(false);
    setSelectedParcel(null);
  }

  const getPlaceholder = () => {
    switch (searchType) {
      case "lr": return "Search by LR number..."
      case "phone": return "Search by phone number..."
      case "sender": return "Search by sender name..."
      case "recipient": return "Search by recipient name..."
      default: return "Search parcels..."
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()

    // Reset to first page and apply search filter
    if (searchQuery.trim()) {
      // For search queries, we'll filter client-side since the API doesn't support
      // all the search types we need (like searching across multiple fields)
      setLoading(true)

      // Fetch all parcels for the current filter set, then filter client-side
      const url = new URL('/api/parcels', window.location.origin)

      // Add filters if any
      if (selectedStatus !== "all") {
        url.searchParams.append('current_status', selectedStatus)
      }

      if (selectedBranch) {
        if (branchType === "sender") {
          url.searchParams.append('sender_branch_id', selectedBranch)
        } else {
          url.searchParams.append('delivery_branch_id', selectedBranch)
        }
      }

      // For LR number searches, we can use the API directly
      if (searchType === "lr_number") {
        url.searchParams.append('lr_number', searchQuery)
      }

      fetch(url.toString())
        .then(response => response.json())
        .then(data => {
          // Filter the results client-side for other search types
          let filtered = data.parcels

          if (searchType !== "lr_number" && searchQuery) {
            filtered = data.parcels.filter((parcel: Parcel) => {
              switch (searchType) {
                case "phone":
                  return (
                    (parcel.sender_phone?.toLowerCase().includes(searchQuery.toLowerCase()) || false) ||
                    (parcel.recipient_phone?.toLowerCase().includes(searchQuery.toLowerCase()) || false)
                  )
                case "sender":
                  return parcel.sender_name.toLowerCase().includes(searchQuery.toLowerCase())
                case "recipient":
                  return parcel.recipient_name.toLowerCase().includes(searchQuery.toLowerCase())
                default:
                  return true
              }
            })
          }

          setParcels(filtered)
          setPagination({
            ...pagination,
            currentPage: 1,
            totalItems: filtered.length,
            totalPages: Math.ceil(filtered.length / pagination.pageSize)
          })
        })
        .catch(error => {
          console.error('Error searching parcels:', error)
          toast({
            title: 'Error',
            description: 'Failed to search parcels',
            variant: 'destructive',
          })
        })
        .finally(() => {
          setLoading(false)
        })
    } else {
      // If search is cleared, reset to normal pagination
      fetchParcels(1)
    }
  }

  // With server-side pagination, we only need to filter by date
  // since other filters are handled by the API
  const filteredParcels = useMemo(() => {
    if (loading) return [];

    // Only apply date filter client-side
    return parcels.filter(parcel => {
      // Date filter
      const matchesDate = selectedDate
        ? format(new Date(parcel.booking_datetime), "yyyy-MM-dd") === format(selectedDate, "yyyy-MM-dd")
        : true;

      return matchesDate;
    });
  }, [parcels, selectedDate, loading]);

  return (
    <div className="space-y-4">
      {/* View Toggle */}
      <Card className="p-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium">
              {showDeliverableOnly ? "Deliverable Parcels" : "All Parcels"}
            </h3>
            <p className="text-sm text-muted-foreground">
              {showDeliverableOnly
                ? "Parcels ready for delivery at your branch"
                : "All parcels with advanced search and filters"
              }
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant={showDeliverableOnly ? "default" : "outline"}
              size="sm"
              onClick={() => setShowDeliverableOnly(true)}
            >
              Deliverable Parcels
            </Button>
            <Button
              variant={!showDeliverableOnly ? "default" : "outline"}
              size="sm"
              onClick={() => setShowDeliverableOnly(false)}
            >
              All Parcels
            </Button>
          </div>
        </div>
      </Card>

      {/* Search and Filters - Only show for All Parcels view */}
      {!showDeliverableOnly && (
        <Card className="p-4">
          {/* Main Search Bar - Make it stack on mobile */}
          <div className="space-y-6">
          <div className="flex flex-col sm:flex-row gap-3">
            <div className="flex-1 flex flex-col sm:flex-row gap-3">
              <div className="w-full sm:w-[140px]">
                <Select value={searchType} onValueChange={setSearchType}>
                  <SelectTrigger className="h-10">
                    <SelectValue placeholder="Search by" />
                  </SelectTrigger>
                  <SelectContent>
                    {searchTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex-1">
                <form onSubmit={handleSearch} className="relative group">
                  <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground transition-colors group-hover:text-primary" />
                  <Input
                    placeholder={getPlaceholder()}
                    className="pl-10 h-10 transition-all border-2 hover:border-primary/50 focus:border-primary"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        handleSearch(e);
                      }
                    }}
                  />
                  {searchQuery && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-2 top-1/2 -translate-y-1/2 h-7 px-2 hover:bg-transparent"
                      onClick={() => setSearchQuery("")}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </form>
              </div>
            </div>
            <Button
              type="submit"
              className="w-full sm:w-auto px-6"
              disabled={!searchQuery.trim()}
              onClick={handleSearch}
            >
              Search
            </Button>
          </div>

          {/* Compact Filters Row - Make it scroll horizontally on mobile */}
          <div className="overflow-x-auto pb-2">
            <div className="flex items-center gap-3 min-w-max">
              <div className="flex items-center gap-3">
                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                  <SelectTrigger className="w-[130px] h-9">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    {Object.entries(statusLabels).map(([value, label]) => (
                      <SelectItem key={value} value={value}>{label}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={branchType} onValueChange={setBranchType}>
                  <SelectTrigger className="w-[130px] h-9">
                    <SelectValue placeholder="Branch Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="sender">Sender Branch</SelectItem>
                    <SelectItem value="receiver">Receiver Branch</SelectItem>
                  </SelectContent>
                </Select>

                {/* Branch Selection */}
                <Select value={selectedBranch} onValueChange={setSelectedBranch}>
                  <SelectTrigger className="w-[150px] h-9">
                    <SelectValue placeholder="Select Branch" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Branches</SelectItem>
                    {branchList.map((branch) => (
                      <SelectItem key={branch.branch_id} value={branch.branch_id.toString()}>
                        {branch.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="h-9 px-3"
                      size="sm"
                    >
                      <CalendarIcon className="h-4 w-4 mr-2" />
                      {selectedDate ? format(selectedDate, "dd MMM yyyy") : "Select Date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={selectedDate}
                      onSelect={(date) => {
                        setSelectedDate(date);
                        // Refresh parcels with the date filter
                        if (date) {
                          // Date filtering is done client-side in the filteredParcels memo
                          // We don't need to call fetchParcels here
                        } else {
                          // If date is cleared, reset to normal pagination
                          fetchParcels(1);
                        }
                      }}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              {/* Clear Filters Button */}
              {(selectedStatus !== 'all' || selectedBranch || selectedDate) && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-9"
                  onClick={() => {
                    setSelectedStatus('all');
                    setSelectedBranch('');
                    setSelectedDate(undefined);
                    // Reset to first page with no filters
                    fetchParcels(1);
                  }}
                >
                  Clear Filters
                </Button>
              )}
            </div>
          </div>

          {/* Active Filters */}
          {(searchQuery || selectedStatus !== 'all' || selectedBranch || selectedDate) && (
            <div className="flex flex-wrap gap-2">
              {searchQuery && (
                <Badge variant="secondary" className="h-6">
                  {searchTypes.find(t => t.value === searchType)?.label}: {searchQuery}
                </Badge>
              )}
              {selectedStatus !== 'all' && (
                <Badge variant="secondary" className="h-6">
                  Status: {statusLabels[selectedStatus]}
                </Badge>
              )}
              {selectedBranch && (
                <Badge variant="secondary" className="h-6">
                  {branchType === 'sender' ? 'Sender' : 'Receiver'}: {selectedBranch}
                </Badge>
              )}
              {selectedDate && (
                <Badge variant="secondary" className="h-6">
                  Date: {format(selectedDate, "dd MMM yyyy")}
                </Badge>
              )}
            </div>
          )}
        </div>
        </Card>
      )}

      {/* Parcels List - Adjust card content for mobile */}
      <div className="grid gap-6">
        {loading ? (
          <Card className="p-6">
            <div className="flex flex-col items-center justify-center h-[300px]">
              <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
              <p className="text-muted-foreground">Loading parcels...</p>
            </div>
          </Card>
        ) : filteredParcels.length === 0 ? (
          <Card className="p-6">
            <div className="flex flex-col items-center justify-center h-[200px]">
              <p className="text-muted-foreground">No parcels found</p>
            </div>
          </Card>
        ) : (
          filteredParcels.map((parcel) => (
            <Card
              key={parcel.parcel_id}
              className="overflow-hidden cursor-pointer hover:border-primary"
              onClick={() => handleParcelClick(parcel)}
            >
              <CardHeader className="flex flex-col sm:flex-row items-start sm:items-center justify-between space-y-2 sm:space-y-0 pb-2">
                <div>
                  <CardTitle className="text-base font-medium">
                    LR: {parcel.lr_number}
                  </CardTitle>
                  <CardDescription>
                    Booked on {new Date(parcel.booking_datetime).toLocaleDateString()}
                  </CardDescription>
                </div>
                <Badge className={statusColors[parcel.current_status as keyof typeof statusColors] || "bg-gray-500"}>
                  {parcel.current_status}
                </Badge>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 sm:grid-cols-2">
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Sender</p>
                    <p className="text-sm text-muted-foreground">{parcel.sender_name}</p>
                    <p className="text-sm text-muted-foreground">{parcel.sender_phone}</p>
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Recipient</p>
                    <p className="text-sm text-muted-foreground">{parcel.recipient_name}</p>
                    <p className="text-sm text-muted-foreground">{parcel.recipient_phone}</p>
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Items</p>
                    <p className="text-sm text-muted-foreground">
                      {parcel.number_of_items || 1} items • {parcel.weight || 0} kg
                    </p>
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Payment</p>
                    <div className="flex items-center gap-2">
                      <Badge variant={parcel.payment_mode === "Paid" ? "success" : "warning"}>
                        {paymentTypeLabels[parcel.payment_mode as keyof typeof paymentTypeLabels]}
                      </Badge>
                      <p className="text-sm text-muted-foreground">₹{parcel.total_amount?.toFixed(2) || '0.00'}</p>
                    </div>
                  </div>
                </div>
                <div className="mt-4 flex flex-col sm:flex-row items-stretch sm:items-center gap-2">
                  {/* Conditional Deliver button - show for deliverable parcels or when in deliverable view */}
                  {(showDeliverableOnly ||
                    (parcel.current_status === "Received" &&
                     parcel.delivery_branch?.branch_id?.toString() === userBranchId)) && (
                    <Button
                      className="flex-1 sm:flex-none"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeliveryClick(parcel);
                      }}
                    >
                      Deliver
                    </Button>
                  )}
                  <Button
                    variant="outline"
                    className="flex-1 sm:flex-none"
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedParcel(parcel);
                      setIsDetailsOpen(true);
                    }}
                  >
                    View Details
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Pagination */}
      {!loading && filteredParcels.length > 0 && (
        <div className="mt-6 flex justify-center">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() => pagination.currentPage > 1 && fetchParcels(pagination.currentPage - 1)}
                  className={pagination.currentPage <= 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                />
              </PaginationItem>

              {/* First page */}
              {pagination.currentPage > 2 && (
                <PaginationItem>
                  <PaginationLink onClick={() => fetchParcels(1)}>1</PaginationLink>
                </PaginationItem>
              )}

              {/* Ellipsis if needed */}
              {pagination.currentPage > 3 && (
                <PaginationItem>
                  <PaginationEllipsis />
                </PaginationItem>
              )}

              {/* Previous page if not on first page */}
              {pagination.currentPage > 1 && (
                <PaginationItem>
                  <PaginationLink onClick={() => fetchParcels(pagination.currentPage - 1)}>
                    {pagination.currentPage - 1}
                  </PaginationLink>
                </PaginationItem>
              )}

              {/* Current page */}
              <PaginationItem>
                <PaginationLink isActive>{pagination.currentPage}</PaginationLink>
              </PaginationItem>

              {/* Next page if not on last page */}
              {pagination.currentPage < pagination.totalPages && (
                <PaginationItem>
                  <PaginationLink onClick={() => fetchParcels(pagination.currentPage + 1)}>
                    {pagination.currentPage + 1}
                  </PaginationLink>
                </PaginationItem>
              )}

              {/* Ellipsis if needed */}
              {pagination.currentPage < pagination.totalPages - 2 && (
                <PaginationItem>
                  <PaginationEllipsis />
                </PaginationItem>
              )}

              {/* Last page if not already shown */}
              {pagination.currentPage < pagination.totalPages - 1 && (
                <PaginationItem>
                  <PaginationLink onClick={() => fetchParcels(pagination.totalPages)}>
                    {pagination.totalPages}
                  </PaginationLink>
                </PaginationItem>
              )}

              <PaginationItem>
                <PaginationNext
                  onClick={() => pagination.currentPage < pagination.totalPages && fetchParcels(pagination.currentPage + 1)}
                  className={pagination.currentPage >= pagination.totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}

      {/* Page info */}
      {!loading && filteredParcels.length > 0 && (
        <div className="mt-2 text-center text-sm text-muted-foreground">
          Showing {filteredParcels.length} of {pagination.totalItems} parcels
        </div>
      )}

      {selectedParcel && (
        <>
          <ParcelDetailsDialog
            open={isDetailsOpen}
            onOpenChange={setIsDetailsOpen}
            parcel={{
              parcel_id: selectedParcel.parcel_id, // Pass the parcel_id for API calls
              lrn: selectedParcel.lr_number,
              senderName: selectedParcel.sender_name,
              senderAddress: selectedParcel.sender_branch?.address || '',
              senderPhone: selectedParcel.sender_phone || '',
              senderBranch: selectedParcel.sender_branch?.name || '',
              recipientName: selectedParcel.recipient_name,
              recipientAddress: selectedParcel.delivery_branch?.address || '',
              recipientPhone: selectedParcel.recipient_phone || '',
              recipientBranch: selectedParcel.delivery_branch?.name || '',
              status: selectedParcel.current_status,
              bookingDate: selectedParcel.booking_datetime,
              weight: selectedParcel.weight?.toString() || '0',
              dimensions: '',
              price: selectedParcel.total_amount?.toString() || '0',
              paymentMode: selectedParcel.payment_mode,
              instructions: '',
              numberOfItems: selectedParcel.number_of_items || 1,
              items: [
                {
                  type: parcelTypes.find(t => t.type_id === selectedParcel.item_type)?.type_name || 'Item',
                  description: '',
                  quantity: selectedParcel.number_of_items || 1,
                  weight: selectedParcel.weight || 0,
                  amount: selectedParcel.total_amount || 0
                }
              ],
              // We'll fetch the status history in the dialog component
              statusHistory: []
            }}
          />
          <ParcelStatusDialog
            open={isStatusUpdateOpen}
            onOpenChange={setIsStatusUpdateOpen}
            parcel={{
              lr: selectedParcel.lr_number,
              status: selectedParcel.current_status,
              paymentType: selectedParcel.payment_mode === 'Paid' ? 'paid' : 'to_pay',
              price: selectedParcel.total_amount?.toString() || '0',
              recipientName: selectedParcel.recipient_name,
              recipientPhone: selectedParcel.recipient_phone || ''
            }}
            onStatusUpdate={handleStatusUpdate}
          />
          <ParcelDeliveryDialog
            isOpen={isDeliveryDialogOpen}
            onClose={() => setIsDeliveryDialogOpen(false)}
            parcel={selectedParcel ? {
              parcel_id: selectedParcel.parcel_id,
              lr_number: selectedParcel.lr_number,
              sender_name: selectedParcel.sender_name,
              recipient_name: selectedParcel.recipient_name,
              recipient_phone: selectedParcel.recipient_phone,
              number_of_items: selectedParcel.number_of_items || 1,
              total_received_items: selectedParcel.number_of_items || 1 // Assuming all items received if status is "Received"
            } : null}
            onDeliveryComplete={handleDeliveryComplete}
          />
        </>
      )}
    </div>
  )
}
